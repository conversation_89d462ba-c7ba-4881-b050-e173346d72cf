import 'dart:async';
import 'dart:io';

import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:just_audio/just_audio.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:stop_watch_timer/stop_watch_timer.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';
import 'package:v_chat_room_page/v_chat_room_page.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import '../../core/agora_user.dart';
import '../../core/call_state.dart';

class VCallController extends ValueNotifier<CallState> {
  VCallController(this.dto) : super(CallState()) {
    debugPrint(
        'LOG::VCallController constructor called with dto: isCaller=${dto.isCaller}, callId=${dto.callId}, roomId=${dto.roomId}');
    _initializeAgora();
    _addListeners();
    unawaited(WakelockPlus.enable());
    CallKeepHandler.I.endCalls(null);
  }

  final VCallDto dto;

  late BuildContext context;
  final _agoraEngine = createAgoraRtcEngine();
  late final RtcEngineEventHandler _eventHandler;
  final AudioPlayer _audioPlayer = AudioPlayer();
  String get channelName {
    // For existing calls (invitations), use the callId as channel name
    // For new calls, use the roomId (which will become the callId)
    return dto.callId ?? dto.roomId!;
  }

  int get userLength => value.users.length;

  StreamSubscription? callStream;
  Timer? _callTimeoutTimer;

  bool get _callerIsVideoEnable => dto.isVideoEnable;

  final stopWatchTimer = StopWatchTimer(
    mode: StopWatchMode.countUp,
  );
  Future<void> disposeRingPlayer() async {
    await _audioPlayer.dispose();
  }

  final _videoConfig = const VideoEncoderConfiguration(
    orientationMode: OrientationMode.orientationModeAdaptive,
  );

  Future<void> _initializeAgora() async {
    debugPrint('LOG::_initializeAgora started');
    // Set aspect ratio for video according to platform

    await _initAgoraRtcEngine();
    debugPrint('LOG::_initAgoraRtcEngine completed');
    _addAgoraEventHandlers();
    debugPrint('LOG::_addAgoraEventHandlers completed');
    if (_callerIsVideoEnable) {
      await _agoraEngine.enableVideo();
      await _agoraEngine.startPreview();
    }

    // // Join the channel
    final userToken = dto.callId != null
        ? await VChatController.I.nativeApi.remote.calls
            .getAgoraAccessForCall(dto.callId!)
        : await VChatController.I.nativeApi.remote.calls
            .getAgoraAccess(channelName);
    if (_callerIsVideoEnable) {
      value.isVideoEnabled = true;
      await _agoraEngine.setVideoEncoderConfiguration(_videoConfig);
      value.isSpeakerEnabled = true;
    }
    // Join the channel
    debugPrint(
        'LOG::Attempting to join channel: $channelName with token: ${userToken.substring(0, 20)}...');
    await _agoraEngine.joinChannel(
      token: userToken,
      channelId: channelName,
      uid: 0,
      options: ChannelMediaOptions(
        autoSubscribeVideo: true,
        autoSubscribeAudio: true,
        clientRoleType: ClientRoleType.clientRoleBroadcaster,
        publishCameraTrack: dto.isVideoEnable,
        publishMicrophoneTrack: true,
      ),
    );
    debugPrint('LOG::joinChannel call completed');

    debugPrint(
        'LOG::Initialization complete. isCaller: ${dto.isCaller}, callId: ${dto.callId}');
    if (dto.isCaller) {
      debugPrint('LOG::Creating call as caller');
      createCall();
    }
    if (dto.callId != null) {
      debugPrint('LOG::Accepting call as receiver');
      _acceptCall();
    }
  }

  Future<void> _onCallEnd() async {
    await _agoraEngine.leaveChannel();
    _agoraEngine.release();
    endCallApi();
  }

  Future<void> _initAgoraRtcEngine() async {
    try {
      await _agoraEngine.initialize(
        const RtcEngineContext(
          appId: SConstants.agoraAppId,
        ),
      );
      await _agoraEngine
          .setChannelProfile(ChannelProfileType.channelProfileCommunication);
    } catch (err) {
      print(err);
      VAppAlert.showSuccessSnackBar(
        message: "$err",
        context: context,
      );
    }
  }

  Future<void> _addAgoraEventHandlers() async {
    _eventHandler = RtcEngineEventHandler(
      onError: (code, String msg) {
        final info = 'LOG::onError: $code Message is  $msg';
        debugPrint(info);
      },

      onConnectionStateChanged: (RtcConnection connection,
          ConnectionStateType state, ConnectionChangedReasonType reason) {
        debugPrint('LOG::connectionStateChanged: $state, reason: $reason');
      },

      ///Indicates that the local user has successfully joined the channel.
      onJoinChannelSuccess: (RtcConnection connection, int elapsed) async {
        final info =
            'LOG::onJoinChannel: ${connection.channelId}, uid: ${connection.localUid}';
        debugPrint(info);
        debugPrint('LOG::Channel join successful, waiting for remote users...');
        value.currentUid = connection.localUid;
        await _agoraEngine.enableAudio();
        await _agoraEngine.muteLocalAudioStream(false);
        await _agoraEngine.muteLocalVideoStream(!_callerIsVideoEnable);

        if (_callerIsVideoEnable) {
          value.isVideoEnabled = true;
          await _agoraEngine.setVideoEncoderConfiguration(_videoConfig);
          await _agoraEngine.setEnableSpeakerphone(true);
          value.isSpeakerEnabled = true;
        }

        final myUser = AgoraUser(
          uid: value.currentUid!,
          isAudioEnabled: true,
          isVideoEnabled: _callerIsVideoEnable,
          view: AgoraVideoView(
            controller: VideoViewController(
              rtcEngine: _agoraEngine,
              canvas: const VideoCanvas(
                uid: 0,
              ),
            ),
          ),
        );
        value.users.add(myUser);
        notifyListeners();
      },
      //for my mic is 100% works
      onFirstLocalAudioFramePublished: (RtcConnection connection, int elapsed) {
        final info = 'LOG::firstLocalAudio: $elapsed';
        debugPrint(info);
        for (AgoraUser user in value.users) {
          if (user.uid == value.currentUid) {
            user.isAudioEnabled = true;
          }
        }
        notifyListeners();
      },
      //for my camera is 100% works
      onFirstLocalVideoFrame: (
        VideoSourceType source,
        int width,
        int height,
        int elapsed,
      ) {
        debugPrint('LOG::firstLocalVideo');
        for (AgoraUser user in value.users) {
          if (user.uid == value.currentUid) {
            user.isVideoEnabled = value.isVideoEnabled;

            // Update the view
            user.view = AgoraVideoView(
              controller: VideoViewController(
                rtcEngine: _agoraEngine,
                canvas: const VideoCanvas(
                  uid: 0, // Use 0 for local video
                ),
              ),
            );
          }
        }
        notifyListeners();
      },
      //called when i call the leave channel api
      onLeaveChannel: (RtcConnection connection, RtcStats stats) {
        debugPrint('LOG::onLeaveChannel');
        value.users.clear();
        notifyListeners();
      },

      ///new remote user has been joined!
      onUserJoined: (RtcConnection connection, int remoteUid, int elapsed) {
        final info = 'LOG::userJoined: $remoteUid';
        debugPrint(info);
        debugPrint('LOG::Total users before adding: ${value.users.length}');
        value.users.add(
          AgoraUser(
            uid: remoteUid,
            isVideoEnabled:
                _callerIsVideoEnable, // Set initial video state based on call type
            isAudioEnabled: true, // Assume audio is enabled by default
            view: AgoraVideoView(
              controller: VideoViewController.remote(
                rtcEngine: _agoraEngine,
                canvas: VideoCanvas(uid: remoteUid),
                connection: RtcConnection(
                  channelId: channelName,
                  localUid: value.currentUid!,
                ),
              ),
            ),
          ),
        );
        debugPrint('LOG::Total users after adding: ${value.users.length}');
        debugPrint('LOG::User UIDs: ${value.users.map((u) => u.uid).toList()}');
        if (value.users.isNotEmpty) {
          disposeRingPlayer();
          if (!stopWatchTimer.isRunning) {
            stopWatchTimer.onStartTimer();
          }
          value.status = VCallStatus.inCall;
        }
        notifyListeners();
      },
      onUserOffline: (RtcConnection connection, int remoteUid,
          UserOfflineReasonType reason) {
        final info = 'LOG::userOffline: $remoteUid, reason: $reason';
        debugPrint(info);
        debugPrint('LOG::Total users before removing: ${value.users.length}');
        AgoraUser? userToRemove;
        for (AgoraUser user in value.users) {
          if (user.uid == remoteUid) {
            userToRemove = user;
          }
        }
        value.users.remove(userToRemove);
        debugPrint('LOG::Total users after removing: ${value.users.length}');
        debugPrint(
            'LOG::Remaining user UIDs: ${value.users.map((u) => u.uid).toList()}');
        notifyListeners();
      },
      onFirstRemoteAudioFrame:
          (RtcConnection connection, int userId, int elapsed) {
        final info = 'LOG::firstRemoteAudio: $userId';
        debugPrint(info);
        for (AgoraUser user in value.users) {
          if (user.uid == userId) {
            user.isAudioEnabled = true;
          }
        }
        notifyListeners();
      },
      onFirstRemoteVideoFrame: (RtcConnection connection, int remoteUid,
          int width, int height, int elapsed) {
        final info = 'LOG::firstRemoteVideo: $remoteUid ${width}x $height';
        debugPrint(info);
        for (AgoraUser user in value.users) {
          if (user.uid == remoteUid) {
            user
              ..isVideoEnabled = true
              ..view = AgoraVideoView(
                controller: VideoViewController.remote(
                  rtcEngine: _agoraEngine,
                  canvas: VideoCanvas(
                    uid: remoteUid,
                  ),
                  connection: connection,
                ),
              );
          }
        }
        notifyListeners();
      },
      onRemoteVideoStateChanged: (RtcConnection connection, int remoteUid,
          RemoteVideoState state, RemoteVideoStateReason reason, int elapsed) {
        final info = 'LOG::remoteVideoStateChanged: $remoteUid $state $reason';
        debugPrint(info);
        for (AgoraUser user in value.users) {
          if (user.uid == remoteUid) {
            user.isVideoEnabled =
                state != RemoteVideoState.remoteVideoStateStopped;
          }
        }
        notifyListeners();
      },
      onTokenPrivilegeWillExpire: (connection, token) {
        final info =
            'LOG::onTokenPrivilegeWillExpire: $connection token $token ';
        debugPrint(info);
      },
      onRemoteAudioStateChanged: (RtcConnection connection, int remoteUid,
          RemoteAudioState state, RemoteAudioStateReason reason, int elapsed) {
        final info = 'LOG::remoteAudioStateChanged: $remoteUid $state $reason';
        debugPrint(info);
        for (AgoraUser user in value.users) {
          if (user.uid == remoteUid) {
            user.isAudioEnabled =
                state != RemoteAudioState.remoteAudioStateStopped;
          }
        }
        notifyListeners();
      },
    );
    _agoraEngine.registerEventHandler(
      _eventHandler,
    );
  }

  void onToggleCamera() {
    value.isVideoEnabled = !value.isVideoEnabled;
    for (final user in value.users) {
      if (user.uid == value.currentUid) {
        user.isVideoEnabled = value.isVideoEnabled;
      }
    }
    notifyListeners();
    _agoraEngine.muteLocalVideoStream(!value.isVideoEnabled);
  }

  void onToggleMicrophone() {
    value.isMicEnabled = !value.isMicEnabled;
    for (AgoraUser user in value.users) {
      if (user.uid == value.currentUid) {
        user.isAudioEnabled = value.isMicEnabled;
      }
    }

    notifyListeners();
    _agoraEngine.muteLocalAudioStream(!value.isMicEnabled);
  }

  void onToggleSpeaker() {
    value.isSpeakerEnabled = !value.isSpeakerEnabled;

    notifyListeners();
    _agoraEngine.setEnableSpeakerphone(value.isSpeakerEnabled);
  }

  void onSwitchCamera() => _agoraEngine.switchCamera();

  void onSwitchAudioVideo() async {
    dto.isVideoEnable = !dto.isVideoEnable;
    if (dto.isVideoEnable) {
      await _agoraEngine.enableVideo();
      await _agoraEngine.startPreview();
    } else {
      await _agoraEngine.disableVideo();
      await _agoraEngine.stopPreview();
    }
    await _agoraEngine.updateChannelMediaOptions(
      ChannelMediaOptions(
        publishCameraTrack: dto.isVideoEnable,
        publishMicrophoneTrack: true,
      ),
    );
    value.isVideoEnabled = dto.isVideoEnable;
    for (final user in value.users) {
      if (user.uid == value.currentUid) {
        user.isVideoEnabled = value.isVideoEnabled;
      }
    }
    notifyListeners();
    await _agoraEngine.muteLocalVideoStream(!dto.isVideoEnable);
    VChatController.I.nativeApi.remote.calls.switchAudioVideo(
      callId: value.callId!,
      withVideo: dto.isVideoEnable,
    );
  }

  void createCall() async {
    try {
      playSoundWithInterval();
      value.callId = await VChatController.I.nativeApi.remote.calls.createCall(
        roomId: dto.roomId!,
        withVideo: dto.isVideoEnable,
      );

      // Start client-side timeout timer as backup (30 seconds)
      _startCallTimeoutTimer();
    } catch (err) {
      VAppAlert.showSuccessSnackBar(message: err.toString(), context: context);
      await Future.delayed(const Duration(milliseconds: 500));
      Navigator.pop(context);
    }
  }

  ///call this once you want to end the call but it must be started
  Future endCallApi() async {
    final meetIdValue = dto.callId ?? value.callId;
    if (meetIdValue == null) return;
    await vSafeApiCall<bool>(
      request: () async {
        return VChatController.I.nativeApi.remote.calls.endCallV2(meetIdValue);
      },
      onSuccess: (_) {},
      onError: (exception, trace) async {},
    );
  }

  /// Starts a client-side timeout timer as backup to server-side timeout
  void _startCallTimeoutTimer() {
    _callTimeoutTimer?.cancel(); // Cancel any existing timer
    _callTimeoutTimer = Timer(const Duration(seconds: 30), () {
      // Only timeout if call is still ringing (not accepted)
      if (value.status == VCallStatus.ring) {
        value.status = VCallStatus.timeout;
        notifyListeners();
        if (context.mounted) {
          Navigator.pop(context);
        }
      }
    });
  }

  /// Cancels the client-side timeout timer
  void _cancelCallTimeoutTimer() {
    _callTimeoutTimer?.cancel();
    _callTimeoutTimer = null;
  }

  void _addListeners() {
    callStream = VChatController.I.nativeApi.streams.callStream.listen(
      (e) async {
        if (e is VCallAcceptedEvent) {
          _cancelCallTimeoutTimer(); // Cancel timeout timer when call is accepted
          debugPrint('LOG::Call accepted event received for room: ${e.roomId}');
          debugPrint(
              'LOG::Event meetId: ${e.data.meetId}, our callId: ${dto.callId ?? value.callId}');

          // Check if this is for our current call
          if (e.roomId == dto.roomId ||
              e.data.meetId == (dto.callId ?? value.callId)) {
            debugPrint(
                'LOG::Call accepted event matches our call, setting status to inCall');
            value.status = VCallStatus.inCall;
            notifyListeners();
            if (!stopWatchTimer.isRunning) {
              stopWatchTimer.onStartTimer();
            }
          } else {
            debugPrint('LOG::Call accepted event does not match our call');
          }
          return;
        }
        if (e is VCallSwitchAudioVideoEvent) {
          dto.isVideoEnable = e.withVideo;
          if (e.withVideo) {
            await _agoraEngine.enableVideo();
            await _agoraEngine.startPreview();
          } else {
            await _agoraEngine.disableVideo();
            await _agoraEngine.stopPreview();
          }
          await _agoraEngine.updateChannelMediaOptions(
            ChannelMediaOptions(
              publishCameraTrack: e.withVideo,
              publishMicrophoneTrack: true,
            ),
          );
          value.isVideoEnabled = e.withVideo;
          for (final user in value.users) {
            if (user.uid == value.currentUid) {
              user.isVideoEnabled = value.isVideoEnabled;
            }
          }
          notifyListeners();
          await _agoraEngine.muteLocalVideoStream(!e.withVideo);
          return;
        }
        if (e is VCallTimeoutEvent) {
          _cancelCallTimeoutTimer(); // Cancel timeout timer when server timeout occurs
          value.status = VCallStatus.timeout;
          notifyListeners();
          Navigator.pop(context);
          return;
        }
        if (e is VCallEndedEvent) {
          debugPrint('LOG::Call ended event received');
          _cancelCallTimeoutTimer(); // Cancel timeout timer when call ends
          value.status = VCallStatus.finished;
          notifyListeners();
          if (context.mounted) {
            Navigator.pop(context);
          }
          return;
        }
        if (e is VCallRejectedEvent) {
          debugPrint('LOG::Call rejected event received');
          _cancelCallTimeoutTimer(); // Cancel timeout timer when call is rejected
          value.status = VCallStatus.rejected;
          notifyListeners();
          Navigator.pop(context);

          return;
        }
        if (e is VCallParticipantJoinedEvent) {
          // Handle participant joined event
          debugPrint(
              'Participant joined: ${e.participant['name']} in room: ${e.roomId}');
          debugPrint(
              'All participants: ${e.allParticipants.map((p) => p['name']).join(', ')}');

          // Check if this is for our current call
          if (e.roomId == dto.roomId) {
            // Update UI or show notification that someone joined
            // The Agora onUserJoined callback will handle the actual video/audio setup
            debugPrint('Participant joined our call: ${e.participant['name']}');
          }
          return;
        }
      },
    );
  }

  void _acceptCall() async {
    debugPrint('LOG::_acceptCall called for callId: ${dto.callId}');
    try {
      await VChatController.I.nativeApi.remote.calls.acceptCall(
        callId: dto.callId!,
      );
      debugPrint('LOG::acceptCall API call successful');
      _cancelCallTimeoutTimer(); // Cancel timeout timer when accepting call
      stopWatchTimer.onStartTimer();

      value.status = VCallStatus.inCall;
      notifyListeners();
      debugPrint('LOG::Call status set to inCall');
    } catch (e) {
      debugPrint('LOG::Error in _acceptCall: $e');
      rethrow;
    }
  }

  /// Invite users to the current call
  Future<void> onInviteUsers(BuildContext context) async {
    try {
      // Import the choose rooms page
      final result = await Navigator.push<List<String>>(
        context,
        CupertinoPageRoute(
          builder: (context) => VChooseRoomsPage(currentRoomId: dto.roomId),
        ),
      );

      if (result != null && result.isNotEmpty) {
        // Send invitations to selected rooms
        await _sendCallInvitations(result);

        // Show success message
        VAppAlert.showSuccessSnackBar(
          message: 'Invitations sent to ${result.length} chat(s)',
          context: context,
        );
      }
    } catch (e) {
      VAppAlert.showErrorSnackBar(
        message: 'Failed to send invitations: $e',
        context: context,
      );
    }
  }

  /// Send call invitations to selected rooms
  Future<void> _sendCallInvitations(List<String> roomIds) async {
    try {
      // Wait for call to be established if needed
      String? callId = dto.callId ?? value.callId;

      // If no callId yet, wait a bit for the call to be created
      if (callId == null && dto.isCaller) {
        // Wait up to 5 seconds for the call to be created
        for (int i = 0; i < 10; i++) {
          await Future.delayed(const Duration(milliseconds: 500));
          callId = value.callId;
          if (callId != null) break;
        }
      }

      if (callId == null || callId.isEmpty) {
        throw Exception(
            'No active call ID found. Please wait for the call to be established.');
      }

      debugPrint('Sending invitations with callId: $callId to rooms: $roomIds');

      await VChatController.I.nativeApi.remote.calls.inviteToCall(
        callId: callId,
        roomIds: roomIds,
      );
    } catch (e) {
      debugPrint('Failed to send invitations: $e');
      rethrow;
    }
  }

  @override
  void dispose() async {
    _cancelCallTimeoutTimer(); // Cancel timeout timer on dispose
    stopWatchTimer.dispose();
    WakelockPlus.disable();
    callStream?.cancel();
    await _onCallEnd();
    disposeRingPlayer();
    super.dispose();
  }

  Future<void> playSoundWithInterval() async {
    final tempDir = await getTemporaryDirectory();
    final tempFile = File(join(tempDir.path, 'temp_audio.mp3'));

    // Check if file exists
    if (!await tempFile.exists()) {
      // File doesn't exist, load from assets and save
      final bytes = await rootBundle
          .load('packages/v_chat_message_page/assets/dialing.mp3');
      final audioBytes = bytes.buffer.asUint8List();
      await tempFile.writeAsBytes(audioBytes);
    }

    // File exists now (either previously or just created)
    await _audioPlayer.setAudioSource(AudioSource.file(tempFile.path));

    await _audioPlayer.setLoopMode(LoopMode.all);
    await _audioPlayer.setVolume(1);
    await _audioPlayer.play();
  }
}
